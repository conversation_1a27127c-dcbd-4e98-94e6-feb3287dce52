import { Button } from '@/components/ui/button';
import { Text } from '@/components/ui/text';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Skeleton } from '@/components/ui/skeleton';
import { getUsageColor } from '@/components/monitor/MonitorCardStyles';
import { useThemeColor } from '@/hooks/useThemeColor';

import { useAppStore } from '@/lib/store';
import { ThreeXUIConfig, MonitoringStatus } from '@/lib/types';
import { smartFetch } from '@/lib/utils';
import { getThreeXUIServerStatus } from '@/panels/3x-ui/utils';
import { BottomSheetBackdrop, BottomSheetModal, BottomSheetScrollView } from '@gorhom/bottom-sheet';
import { router, useLocalSearchParams } from 'expo-router';
import { RefreshCw, Download, ArrowUp, ArrowDown } from 'lucide-react-native';
import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { SafeAreaView, ScrollView, StyleSheet, View, Alert, TouchableOpacity } from 'react-native';
import { Pie, PolarChart, CartesianChart, Area } from 'victory-native';
import { RadioGroup, RadioGroupItem } from '~/components/ui/radio-group';
import { Label } from '~/components/ui/label';
import { useFocusEffect } from '@react-navigation/native';

// 动画饼图切片组件
function AnimatedPieSlice({ slice }: { slice: any }) {
  return (
    <Pie.Slice
      animate={{
        type: "timing",
        duration: 500,
      }}
    />
  );
}

// 动画饼图组件
interface AnimatedPieChartProps {
  data: Array<{ label: string; value: number; color: string }>;
  size: number;
  innerRadius?: number | string;
}

function AnimatedPieChart({ data, size, innerRadius = "85%" }: AnimatedPieChartProps) {
  return (
    <View style={{ width: size, height: size }}>
      <PolarChart
        data={data}
        labelKey="label"
        valueKey="value"
        colorKey="color"
      >
        <Pie.Chart innerRadius={innerRadius}>
          {({ slice }) => <AnimatedPieSlice slice={slice} />}
        </Pie.Chart>
      </PolarChart>
    </View>
  );
}

export default function ThreeXUIOverviewScreen() {
  const { configId } = useLocalSearchParams<{ configId: string }>();
  const backgroundColor = useThemeColor({}, 'background');
  const borderColor = useThemeColor({}, 'border');
  const textColor = useThemeColor({}, 'text');

  const { configs, getMonitoringStatus, setMonitoringStatus, getProxyServer } = useAppStore();

  // 直接从configs中计算config，无需useState和useEffect
  const config = useMemo(() => {
    if (!configId) return null;
    return configs.find(c => c.id === configId) as ThreeXUIConfig || null;
  }, [configId, configs]);

  const [isRestarting, setIsRestarting] = useState(false);
  const [xrayVersions, setXrayVersions] = useState<string[]>([]);
  const [selectedVersion, setSelectedVersion] = useState<string>('');
  const [isInstalling, setIsInstalling] = useState(false);
  // 初始化网络历史数据，始终保持6组数据
  const [networkHistory, setNetworkHistory] = useState<Array<{ up: number; down: number; timestamp: number }>>(() => {
    const initialData = [];
    const now = Date.now();
    for (let i = 0; i < 6; i++) {
      initialData.push({
        up: 0,
        down: 0,
        timestamp: now - (6 - i) * 1500 // 1.5秒间隔
      });
    }
    return initialData;
  });

  // 数据获取相关状态
  const intervalRef = useRef<any>(null);
  const lastUpdateTimeRef = useRef<number>(0);
  const failureCountRef = useRef<number>(0);

  // BottomSheet refs
  const versionBottomSheetRef = useRef<BottomSheetModal>(null);
  const snapPoints = useMemo(() => ['70%'], []);

  // 检查配置有效性
  useEffect(() => {
    if (!configId) {
      Alert.alert('错误', '未找到配置ID');
      router.back();
      return;
    }

    if (!config) {
      Alert.alert('错误', '未找到对应的3X-UI配置');
      router.back();
      return;
    }
  }, [configId, config]);

  const status = config ? getMonitoringStatus(config.id) : null;

  // 获取服务器状态
  const fetchServerStatus = async () => {
    if (!config) return;

    const requestTime = Date.now();

    try {
      // 检查是否有中转服务器配置
      const proxyServer = getProxyServer();
      if (proxyServer) {
        // TODO: 中转服务器逻辑暂不实现
        console.log('Proxy server configured but not implemented yet');
        return;
      }

      // 直接获取服务器状态
      const serverStatus = await getThreeXUIServerStatus(config);
      if (!serverStatus) {
        throw new Error('Failed to get server status');
      }

      // 检查是否是最新的请求响应
      if (requestTime < lastUpdateTimeRef.current) {
        console.log('Ignoring outdated response for config:', config.id);
        return;
      }

      // 更新最后更新时间
      lastUpdateTimeRef.current = requestTime;

      // 重置失败计数
      failureCountRef.current = 0;

      const newStatus: MonitoringStatus = {
        isOnline: true,
        lastUpdate: new Date(requestTime).toISOString(),
        failureCount: 0,
        serverStatus,
      };

      setMonitoringStatus(config.id, newStatus);
    } catch (error) {
      console.error('Failed to fetch server status for config:', config.id, error);

      // 检查是否是最新的请求响应
      if (requestTime < lastUpdateTimeRef.current) {
        console.log('Ignoring outdated error response');
        return;
      }

      // 更新最后更新时间
      lastUpdateTimeRef.current = requestTime;

      // 增加失败计数
      failureCountRef.current += 1;

      // 3次失败后标记为离线
      if (failureCountRef.current >= 3) {
        const offlineStatus: MonitoringStatus = {
          isOnline: false,
          lastUpdate: new Date(requestTime).toISOString(),
          failureCount: failureCountRef.current,
        };
        setMonitoringStatus(config.id, offlineStatus);
      }
    }
  };

  // 开始轮询
  const startPolling = async () => {
    // 如果已经在轮询或没有配置，直接返回
    if (intervalRef.current || !config) return;

    // 立即获取一次状态
    await fetchServerStatus();

    // 设置轮询间隔 - 每1.5秒轮询一次
    intervalRef.current = setInterval(async () => {
      await fetchServerStatus();
    }, 1500);
  };

  // 停止轮询
  const stopPolling = () => {
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
      intervalRef.current = null;
    }
  };

  // 使用 useFocusEffect 管理轮询
  useFocusEffect(
    useCallback(() => {
      if (config) {
        startPolling()
      }

      return () => {
        // 当组件失去焦点时停止轮询
        stopPolling();
      };
    }, [config])
  );

  // 更新网络历史数据
  useEffect(() => {
    if (status?.serverStatus?.netIO) {
      const newEntry = {
        up: status.serverStatus.netIO.up,
        down: status.serverStatus.netIO.down,
        timestamp: Date.now()
      };

      setNetworkHistory(prev => {
        // 移除第一个元素，添加新数据到末尾，始终保持6组数据
        const updated = [...prev.slice(1), newEntry];
        return updated;
      });
    }
  }, [status?.serverStatus?.netIO]);

  // 重启Xray服务
  const handleRestartXray = async () => {
    if (!config) return;

    setIsRestarting(true);
    try {
      const baseUrl = `${config.protocol}://${config.url}`;
      const restartUrl = `${baseUrl}/server/restartXrayService`;

      const response = await smartFetch(restartUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
        },
        credentials: 'include'
      }, config);

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const result = await response.json();
      if (result.success) {
        Alert.alert('成功', 'Xray服务重启成功');
      } else {
        Alert.alert('错误', 'Xray服务重启失败');
      }
    } catch (error) {
      Alert.alert('错误', 'Xray服务重启失败');
    } finally {
      setIsRestarting(false);
    }
  };

  // 获取Xray版本列表
  const fetchXrayVersions = async () => {
    if (!config) return;

    try {
      const baseUrl = `${config.protocol}://${config.url}`;
      const versionsUrl = `${baseUrl}/server/getXrayVersion`;

      const response = await smartFetch(versionsUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
        },
        credentials: 'include'
      }, config);

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const result = await response.json();
      if (result.success && result.obj) {
        setXrayVersions(result.obj);
        if (result.obj.length > 0) {
          setSelectedVersion(result.obj[0]);
        }
      }
    } catch (error) {
      console.error('Failed to fetch Xray versions:', error);
    }
  };

  // 显示版本选择底部弹窗
  const handleShowVersions = () => {
    fetchXrayVersions();
    versionBottomSheetRef.current?.present();
  };

  // 安装选定版本
  const handleInstallVersion = async () => {
    if (!config || !selectedVersion) return;

    setIsInstalling(true);
    try {
      const baseUrl = `${config.protocol}://${config.url}`;
      const installUrl = `${baseUrl}/server/installXray/${selectedVersion}`;

      const response = await smartFetch(installUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
        },
        credentials: 'include'
      }, config);

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const result = await response.json();
      if (result.success) {
        Alert.alert('成功', `Xray ${selectedVersion} 安装成功`);
        versionBottomSheetRef.current?.dismiss();
      } else {
        Alert.alert('错误', result.msg || 'Xray安装失败');
      }
    } catch (error) {
      Alert.alert('错误', 'Xray安装失败');
    } finally {
      setIsInstalling(false);
    }
  };

  // 格式化字节数
  const formatBytes = (bytes: number): string => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
  };

  // 格式化速度
  const formatSpeed = (bytesPerSecond: number): string => {
    return formatBytes(bytesPerSecond) + '/s';
  };



  // 渲染背景
  const renderBackdrop = useCallback(
    (props: any) => (
      <BottomSheetBackdrop
        {...props}
        disappearsOnIndex={-1}
        appearsOnIndex={0}
        opacity={0.5}
      />
    ),
    []
  );

  // 渲染底部弹窗footer
  const renderFooter = useCallback(
    (props: any) => (
      <View style={[styles.bottomSheetFooter, { backgroundColor, borderTopColor: borderColor }]}>
        <Button
          onPress={handleInstallVersion}
          disabled={isInstalling || !selectedVersion}
          style={[styles.installButton, { opacity: !selectedVersion ? 0.5 : 1 }]}
        >
          <Text style={styles.installButtonText}>
            {isInstalling ? '安装中...' : '安装'}
          </Text>
        </Button>
      </View>
    ),
    [isInstalling, selectedVersion, backgroundColor, borderColor]
  );

  if (!config) {
    return (
      <SafeAreaView style={[styles.container, { backgroundColor }]}>
        <View style={styles.loadingContainer}>
          <Text style={[styles.loadingText, { color: textColor }]}>
            加载中...
          </Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <>
      <SafeAreaView style={[styles.container, { backgroundColor }]}>
        <ScrollView style={styles.content}>
          {!status || !status.isOnline ? (
            <View style={styles.noDataContainer}>
              <Text style={[styles.noDataText, { color: textColor + '60' }]}>
                No Data
              </Text>
              <Text style={[styles.noDataSubtext, { color: textColor + '40' }]}>
                {status?.failureCount && status.failureCount >= 3
                  ? '服务器离线或连接失败'
                  : '正在获取服务器数据...'}
              </Text>
            </View>
          ) : (
            <>
              {/* Xray 状态卡片 */}
              <View style={styles.section}>
                <View style={styles.sectionHeader}>
                  <Text style={[styles.sectionTitle, { color: textColor }]}>
                    Xray 状态
                  </Text>
                  <View style={styles.xrayActions}>
                    <TouchableOpacity
                      style={[styles.actionButton, { borderColor }]}
                      onPress={handleRestartXray}
                      disabled={isRestarting}
                    >
                      <RefreshCw
                        size={16}
                        color={textColor}
                        style={isRestarting ? { opacity: 0.5 } : {}}
                      />
                      <Text style={[styles.actionButtonText, { color: textColor }]}>
                        {isRestarting ? '重启中...' : '重启'}
                      </Text>
                    </TouchableOpacity>
                    <TouchableOpacity
                      style={[styles.actionButton, { borderColor }]}
                      onPress={handleShowVersions}
                    >
                      <Download size={16} color={textColor} />
                      <Text style={[styles.actionButtonText, { color: textColor }]}>
                        更新
                      </Text>
                    </TouchableOpacity>
                  </View>
                </View>

                {status.serverStatus && (
                  <View style={[styles.xrayCard, { backgroundColor: backgroundColor, borderColor }]}>
                    <View style={styles.xrayInfo}>
                      <Badge
                        variant={status.serverStatus.xray.state === 'running' ? 'default' : 'destructive'}
                        style={styles.xrayStatusBadge}
                      >
                        <Text style={styles.badgeText}>
                          {status.serverStatus.xray.state}
                        </Text>
                      </Badge>
                      <Text style={[styles.xrayVersion, { color: textColor + '80' }]}>
                        版本: {status.serverStatus.xray.version}
                      </Text>
                    </View>
                  </View>
                )}
              </View>

              {/* IP 信息 */}
              {status.serverStatus && (
                <View style={styles.section}>
                  <Text style={[styles.sectionTitle, { color: textColor }]}>
                    IP
                  </Text>
                  <View style={styles.ipRow}>
                    <View style={styles.ipItem}>
                      <Text style={[styles.ipText, { color: textColor }]}>
                        v4: {status.serverStatus.publicIP.ipv4}
                      </Text>
                    </View>
                    <Separator orientation="vertical" style={styles.ipSeparator} />
                    <View style={styles.ipItem}>
                      <Text style={[styles.ipText, { color: textColor }]}>
                        v6: {status.serverStatus.publicIP.ipv6}
                      </Text>
                    </View>
                  </View>
                </View>
              )}

              {/* 系统资源圆环图 */}
              {status.serverStatus && (
                <View style={styles.section}>
                  <Text style={[styles.sectionTitle, { color: textColor }]}>
                    系统资源
                  </Text>

                  {/* 一行显示系统资源图表 */}
                  <View style={styles.systemResourcesRow}>
                    {/* CPU */}
                    <View style={styles.systemResourceItem}>
                      <View style={styles.largePieContainer}>
                        <AnimatedPieChart
                          data={[
                            {
                              label: 'Used',
                              value: status.serverStatus.cpu,
                              color: getUsageColor(status.serverStatus.cpu)
                            },
                            {
                              label: 'Free',
                              value: 100 - status.serverStatus.cpu,
                              color: '#e5e7eb'
                            }
                          ]}
                          size={100}
                        />
                        <View style={styles.largeChartCenter}>
                          <Text style={[styles.largeChartCenterLabel, { color: textColor }]}>
                            CPU
                          </Text>
                          <Text style={[styles.largeChartCenterText, { color: textColor }]}>
                            {status.serverStatus.cpu.toFixed(1)}%
                          </Text>
                        </View>
                      </View>
                      <Text style={[styles.largeChartBottomText, { color: textColor }]}>
                        {status.serverStatus.cpuCores} cores
                      </Text>
                    </View>

                    {/* 内存 */}
                    <View style={styles.systemResourceItem}>
                      <View style={styles.largePieContainer}>
                        <AnimatedPieChart
                          data={[
                            {
                              label: 'Used',
                              value: (status.serverStatus.mem.current / status.serverStatus.mem.total) * 100,
                              color: getUsageColor((status.serverStatus.mem.current / status.serverStatus.mem.total) * 100)
                            },
                            {
                              label: 'Free',
                              value: 100 - (status.serverStatus.mem.current / status.serverStatus.mem.total) * 100,
                              color: '#e5e7eb'
                            }
                          ]}
                          size={100}
                        />
                        <View style={styles.largeChartCenter}>
                          <Text style={[styles.largeChartCenterLabel, { color: textColor }]}>
                            内存
                          </Text>
                          <Text style={[styles.largeChartCenterText, { color: textColor }]}>
                            {((status.serverStatus.mem.current / status.serverStatus.mem.total) * 100).toFixed(1)}%
                          </Text>
                        </View>
                      </View>
                      <Text style={[styles.largeChartBottomText, { color: textColor }]}>
                        {formatBytes(status.serverStatus.mem.current)} / {formatBytes(status.serverStatus.mem.total)}
                      </Text>
                    </View>

                    {/* 磁盘 */}
                    <View style={styles.systemResourceItem}>
                      <View style={styles.largePieContainer}>
                        <AnimatedPieChart
                          data={[
                            {
                              label: 'Used',
                              value: (status.serverStatus.disk.current / status.serverStatus.disk.total) * 100,
                              color: getUsageColor((status.serverStatus.disk.current / status.serverStatus.disk.total) * 100)
                            },
                            {
                              label: 'Free',
                              value: 100 - (status.serverStatus.disk.current / status.serverStatus.disk.total) * 100,
                              color: '#e5e7eb'
                            }
                          ]}
                          size={100}
                        />
                        <View style={styles.largeChartCenter}>
                          <Text style={[styles.largeChartCenterLabel, { color: textColor }]}>
                            磁盘
                          </Text>
                          <Text style={[styles.largeChartCenterText, { color: textColor }]}>
                            {((status.serverStatus.disk.current / status.serverStatus.disk.total) * 100).toFixed(1)}%
                          </Text>
                        </View>
                      </View>
                      <Text style={[styles.largeChartBottomText, { color: textColor }]}>
                        {formatBytes(status.serverStatus.disk.current)} / {formatBytes(status.serverStatus.disk.total)}
                      </Text>
                    </View>
                  </View>
                </View>
              )}

              {/* 网络流量图表 */}
              {status.serverStatus && (
                <View style={styles.section}>
                  <Text style={[styles.sectionTitle, { color: textColor }]}>
                    网络流量
                  </Text>

                  <View style={styles.networkChartContainer}>
                    <CartesianChart
                      data={networkHistory.map((item, index) => ({
                        x: index,
                        up: item.up / 1024, // 转换为KB/s
                        down: item.down / 1024
                      }))}
                      xKey="x"
                      yKeys={["up", "down"]}

                    >
                      {({ points, chartBounds }) => (
                        <>
                          {/* 下行流量区域图 */}
                          <Area
                            points={points.down}
                            y0={chartBounds.bottom}
                            color="#334D5C"
                            opacity={0.5}
                            animate={{ type: "timing", duration: 300 }}
                          />
                          {/* 上行流量区域图 */}
                          <Area
                            points={points.up}
                            y0={chartBounds.bottom}
                            color="#DF5A49"
                            opacity={0.5}
                            animate={{ type: "timing", duration: 300 }}
                          />
                        </>
                      )}
                    </CartesianChart>
                  </View>

                  {/* 网络统计 - 一行显示 */}
                  <View style={styles.networkStatsRow}>
                    <View style={styles.networkStatGroup}>
                      <ArrowUp size={18} color="#DF5A49" />
                      <Text style={[styles.networkStatText, { color: textColor }]}>
                        {formatSpeed(status.serverStatus.netIO.up)}
                      </Text>
                      <Text style={[styles.networkStatValue, { color: textColor }]}>
                        {formatBytes(status.serverStatus.netTraffic.sent)}
                      </Text>
                    </View>

                    <View style={styles.networkStatGroup}>
                      <ArrowDown size={18} color="#334D5C" />
                      <Text style={[styles.networkStatText, { color: textColor }]}>
                        {formatSpeed(status.serverStatus.netIO.down)}
                      </Text>
                      <Text style={[styles.networkStatValue, { color: textColor }]}>
                        {formatBytes(status.serverStatus.netTraffic.recv)}
                      </Text>
                    </View>
                  </View>
                </View>
              )}
            </>
          )}
        </ScrollView>
      </SafeAreaView>

      {/* 版本选择底部弹窗 */}
      <BottomSheetModal
        ref={versionBottomSheetRef}
        index={1}
        snapPoints={snapPoints}
        backdropComponent={renderBackdrop}
        footerComponent={renderFooter}
        backgroundStyle={{ backgroundColor }}
        handleIndicatorStyle={{ backgroundColor: borderColor }}
        enablePanDownToClose={true}
        enableOverDrag={false}
      >
        <BottomSheetScrollView style={[styles.bottomSheetContent, { backgroundColor }]}>
          <Text style={[styles.bottomSheetTitle, { color: textColor }]}>
            选择 Xray 版本
          </Text>

          {xrayVersions.length === 0 ? (
            <View style={styles.skeletonContainer}>
              {/* 版本列表skeleton */}
              {Array.from({ length: 5 }).map((_, index) => (
                <View key={index} style={styles.skeletonItem}>
                  <Skeleton className="w-4 h-4 rounded-full" />
                  <Skeleton className="flex-1 h-4 ml-3" />
                </View>
              ))}
            </View>
          ) : (
            <RadioGroup value={selectedVersion} onValueChange={setSelectedVersion}>
              {xrayVersions.map((version) => (
                <View key={version} style={styles.radioItem}>
                  <RadioGroupItem value={version} />
                  <Label style={[styles.radioLabel, { color: textColor }]}>
                    {version}
                  </Label>
                </View>
              ))}
            </RadioGroup>
          )}
        </BottomSheetScrollView>
      </BottomSheetModal>
    </>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    flex: 1,
    padding: 16,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  loadingText: {
    fontSize: 16,
  },
  noDataContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 60,
  },
  section: {
    marginBottom: 24,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
  },
  xrayActions: {
    flexDirection: 'row',
    gap: 8,
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 6,
    borderWidth: 1,
    gap: 4,
  },
  actionButtonText: {
    fontSize: 12,
    fontWeight: '500',
  },
  configName: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  configUrl: {
    fontSize: 14,
  },
  noDataText: {
    fontSize: 24,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 8,
    lineHeight: 32, // 添加行高防止字体被裁剪
  },
  noDataSubtext: {
    fontSize: 14,
    textAlign: 'center',
  },
  xrayCard: {
    padding: 16,
    borderRadius: 8,
    borderWidth: 1,
  },
  xrayInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  xrayStatusBadge: {
    marginRight: 12,
  },
  badgeText: {
    fontSize: 12,
    fontWeight: '500',
  },
  xrayVersion: {
    fontSize: 14,
  },
  ipRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    gap: 16,
    paddingHorizontal: 8,
    paddingTop:16
  },
  ipItem: {
    flex: 1,
  },
  ipText: {
    fontSize: 14,
    fontWeight: '500',
  },
  ipSeparator: {
    height: 20,
    width: 1,
  },

  // 系统资源一行显示的样式
  systemResourcesRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginTop: 20,
    flexWrap: 'wrap',
  },
  systemResourceItem: {
    alignItems: 'center',
    width: '32%',
  },
  largePieContainer: {
    position: 'relative',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 6,
  },
  largeChartCenter: {
    position: 'absolute',
    alignItems: 'center',
    justifyContent: 'center',
    width: 110,
    height: 110,
  },
  largeChartCenterLabel: {
    fontSize: 12,
    fontWeight: '600',
    textAlign: 'center',
    marginBottom: 2,
  },
  largeChartCenterText: {
    fontSize: 14,
    fontWeight: '700',
    textAlign: 'center',
  },
  largeChartBottomText: {
    fontSize: 11,
    textAlign: 'center',
    marginTop: 4,
    fontWeight: '600',
  },
  networkChartContainer: {
    marginTop:25,
    height: 150,
    marginBottom: 12,
  },

  networkStatText: {
    fontSize: 14,
    fontWeight: '500',
  },
  networkStatsRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 8,
    gap: 16,
  },
  networkStatGroup: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
    width: '48%',
  },

  networkStatValue: {
    fontSize: 14,
    fontWeight: '500',
  },
  bottomSheetContent: {
    flex: 1,
    padding: 16,
    paddingBottom: 0, // footer会处理底部间距
  },
  bottomSheetTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 16,
    textAlign: 'center',
  },
  bottomSheetFooter: {
    padding: 16,
    borderTopWidth: 1,
  },
  skeletonContainer: {
    paddingVertical: 8,
  },
  skeletonItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    gap: 12,
  },
  radioItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 8,
    gap: 12,
  },
  radioLabel: {
    fontSize: 16,
  },
  installButton: {
    width: '100%',
  },
  installButtonText: {
    fontSize: 16,
    fontWeight: '600',
  },
});
