import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Text } from '@/components/ui/text';

import { useThemeColor } from '@/hooks/useThemeColor';
import { InboundConfig } from '../types';
import { XUIConfigConverter } from '@/panels/3x-ui/utils';
import { Copy } from 'lucide-react-native';
import * as Clipboard from 'expo-clipboard';
import React, { useEffect, useState } from 'react';
import { StyleSheet, View, ScrollView, Alert, TouchableOpacity, Modal } from 'react-native';

interface ExportLinksModalProps {
  visible: boolean;
  onClose: () => void;
  inbound: InboundConfig | null;
  serverHost: string;
}

interface UserLink {
  label: string;
  link: string;
}

export default function ExportLinksModal({ visible, onClose, inbound, serverHost }: ExportLinksModalProps) {
  const textColor = useThemeColor({}, 'text');
  const borderColor = useThemeColor({}, 'border');
  const backgroundColor = useThemeColor({}, 'background');
  
  const [userLinks, setUserLinks] = useState<UserLink[]>([]);

  useEffect(() => {
    if (visible && inbound && serverHost) {
      generateLinks();
    }
  }, [visible, inbound, serverHost]);

  const generateLinks = () => {
    if (!inbound || !serverHost) return;

    try {
      const converter = new XUIConfigConverter(serverHost);
      const links = converter.convertToClientLinks(inbound);
      
      // 解析用户信息
      const settings = typeof inbound.settings === 'string' 
        ? JSON.parse(inbound.settings) 
        : inbound.settings;

      const clients = settings?.clients || [];
      
      const userLinkList: UserLink[] = [];

      // 为每个生成的链接匹配对应的客户端
      links.forEach((link: string, index: number) => {
        const client = clients[index];
        const label = client?.email || `用户 ${index + 1}`;
        userLinkList.push({
          label,
          link
        });
      });

      setUserLinks(userLinkList);
    } catch (error) {
      console.error('Generate links failed:', error);
      Alert.alert('错误', '生成链接失败');
    }
  };

  const copyToClipboard = async (text: string, label: string) => {
    try {
      await Clipboard.setStringAsync(text);
      Alert.alert('成功', `${label} 的链接已复制到剪贴板`);
    } catch (error) {
      Alert.alert('错误', '复制失败');
    }
  };



  return (
    <Modal
      visible={visible}
      transparent={true}
      animationType="fade"
      onRequestClose={onClose}
    >
      <View style={styles.modalOverlay}>
        <View style={[styles.modalContent, { backgroundColor }]}>
          {/* 标题 */}
          <View style={styles.modalHeader}>
            <Text style={[styles.modalTitle, { color: textColor }]}>
              导出链接
            </Text>
          </View>

          <View style={styles.content}>
            {userLinks.length === 0 ? (
              <View style={styles.emptyContainer}>
                <Text style={[styles.emptyText, { color: textColor + '80' }]}>
                  没有可用的用户链接
                </Text>
              </View>
            ) : (
              <ScrollView style={styles.linksList} showsVerticalScrollIndicator={false}>
                {userLinks.map((item, index) => (
                  <View onStartShouldSetResponder={() => true} key={index} style={styles.linkItem}>
                    <View style={styles.labelRow}>
                      <Label style={[styles.linkLabel, { color: textColor }]}>
                        {item.label}
                      </Label>
                      <TouchableOpacity
                        onPress={() => copyToClipboard(item.link, item.label)}
                        style={styles.copyIcon}
                      >
                        <Copy size={16} color={textColor} />
                      </TouchableOpacity>
                    </View>
                    <View style={[styles.linkContainer, { borderColor, backgroundColor }]}>
                      <Text style={[styles.linkText, { color: textColor }]}>
                        {item.link}
                      </Text>
                    </View>
                  </View>
                ))}
              </ScrollView>
            )}

            <View style={styles.actions}>
              <Button
                onPress={onClose}
                style={styles.actionButton}
              >
                <Text style={styles.closeText}>关闭</Text>
              </Button>
            </View>
          </View>
        </View>
      </View>
    </Modal>
  );
}

const styles = StyleSheet.create({
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  modalContent: {
    width: '100%',
    maxWidth: 400,
    maxHeight: '80%',
    borderRadius: 12,
    padding: 0,
    elevation: 5,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
  },
  modalHeader: {
    padding: 20,
    paddingBottom: 16,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0, 0, 0, 0.1)',
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: '600',
    textAlign: 'center',
  },
  content: {
    gap: 16,
    padding: 20,
  },
  emptyContainer: {
    padding: 32,
    alignItems: 'center',
  },
  emptyText: {
    fontSize: 14,
  },
  linksList: {
    maxHeight:500
  },
  linkItem: {
    paddingBottom:12
  },
  labelRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 6,
  },
  linkLabel: {
    fontSize: 14,
    fontWeight: '500',
  },
  copyIcon: {
    padding: 4,
  },
  linkContainer: {
    borderWidth: 1,
    borderRadius: 6,
    padding: 12,
    minHeight: 50,
  },
  linkText: {
    fontSize: 12,
    lineHeight: 16,
    fontFamily: 'monospace',
  },
  actions: {
    gap: 8,
  },
  actionButton: {
    paddingVertical: 12,
  },
  closeText: {
    fontSize: 16,
    fontWeight: '500',
  },
});
